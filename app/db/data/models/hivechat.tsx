import { LLMModel, LLMModelProvider } from "@/types/llm"
export const provider: LLMModelProvider = {
  id: 'hivechat',
  providerName: 'HiveChat',
  apiStyle: 'openai_response',
}

export const modelList: LLMModel[] = [
  {
    'id': 'openai/gpt-4.1',
    'displayName': 'GPT 4.1',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'openai/gpt-4.1-mini',
    'displayName': 'GPT 4.1 mini',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'openai/gpt-4.1-nano',
    'displayName': 'GPT 4.1 nano',
    "maxTokens": 1024 * 1024,
    'supportVision': true,
    'selected': false,
    provider
  },
  {
    'id': 'anthropic/claude-sonnet-4',
    'displayName': 'Claude Sonnet 4',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'anthropic/claude-opus-4',
    'displayName': '<PERSON> Opus 4',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'anthropic/claude-3.7-sonnet',
    'displayName': 'Claude Sonnet 3.7',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'anthropic/claude-3.5-haiku',
    'displayName': 'Claude Haiku 3.5',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': false,
    provider
  },
  {
    'id': 'google/gemini-2.5-pro',
    'displayName': 'Gemini 2.5 Pro',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'google/gemini-2.5-flash',
    'displayName': 'Gemini 2.5 Flash',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'deepseek/deepseek-chat-v3-0324',
    'displayName': 'Deepseek V3',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 160 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'deepseek/deepseek-r1-0528',
    'displayName': 'Deepseek R1',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': true,
    provider
  },
  {
    'id': 'moonshotai/kimi-k2',
    'displayName': 'Kimi K2',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 128 * 1024,
    'selected': true,
    provider
  },
]